//
//  UnifiedContactViewTests.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import XCTest
import SwiftUI
@testable import FirmBond

class UnifiedContactViewTests: XCTestCase {
    
    var viewModel: ContactsViewModel!
    var testContext: NSManagedObjectContext!
    
    override func setUp() {
        super.setUp()
        testContext = PersistenceController.preview.container.viewContext
        viewModel = ContactsViewModel(viewContext: testContext)
    }
    
    override func tearDown() {
        viewModel = nil
        testContext = nil
        super.tearDown()
    }
    
    // MARK: - Mode Tests
    
    func testAddMode() {
        let view = UnifiedContactView(mode: .add, viewModel: viewModel)
        XCTAssertEqual(view.currentMode, .add)
        XCTAssertTrue(view.currentMode.isEditing)
        XCTAssertNil(view.currentMode.person)
    }
    
    func testViewMode() {
        let person = createTestPerson()
        let view = UnifiedContactView(mode: .view(person), viewModel: viewModel)
        XCTAssertEqual(view.currentMode, .view(person))
        XCTAssertFalse(view.currentMode.isEditing)
        XCTAssertNotNil(view.currentMode.person)
    }
    
    func testEditMode() {
        let person = createTestPerson()
        let view = UnifiedContactView(mode: .edit(person), viewModel: viewModel)
        XCTAssertEqual(view.currentMode, .edit(person))
        XCTAssertTrue(view.currentMode.isEditing)
        XCTAssertNotNil(view.currentMode.person)
    }
    
    // MARK: - Smart Action Tests
    
    func testPrimaryActionWithPhone() {
        let person = createTestPerson()
        person.phoneNumber = "+1234567890"
        person.email = "<EMAIL>"
        
        let view = UnifiedContactView(mode: .view(person), viewModel: viewModel)
        let primaryAction = view.getPrimaryAction()
        
        XCTAssertNotNil(primaryAction)
        XCTAssertEqual(primaryAction?.icon, "phone.fill")
        XCTAssertTrue(primaryAction?.title.contains("Call") ?? false)
    }
    
    func testPrimaryActionWithEmailOnly() {
        let person = createTestPerson()
        person.email = "<EMAIL>"
        
        let view = UnifiedContactView(mode: .view(person), viewModel: viewModel)
        let primaryAction = view.getPrimaryAction()
        
        XCTAssertNotNil(primaryAction)
        XCTAssertEqual(primaryAction?.icon, "envelope.fill")
        XCTAssertTrue(primaryAction?.title.contains("Email") ?? false)
    }
    
    func testSecondaryActions() {
        let person = createTestPerson()
        person.phoneNumber = "+1234567890"
        person.email = "<EMAIL>"
        
        let view = UnifiedContactView(mode: .view(person), viewModel: viewModel)
        let secondaryActions = view.getSecondaryActions()
        
        XCTAssertTrue(secondaryActions.count > 0)
        XCTAssertTrue(view.hasSecondaryActions())
    }
    
    // MARK: - Form Validation Tests
    
    func testCanSaveAddMode() {
        let view = UnifiedContactView(mode: .add, viewModel: viewModel)
        view.firstName = "John"
        view.lastName = "Doe"
        
        XCTAssertTrue(view.canSave)
    }
    
    func testCannotSaveWithoutRequiredFields() {
        let view = UnifiedContactView(mode: .add, viewModel: viewModel)
        view.firstName = ""
        view.lastName = "Doe"
        
        XCTAssertFalse(view.canSave)
    }
    
    func testCanSaveEditModeWithChanges() {
        let person = createTestPerson()
        let view = UnifiedContactView(mode: .edit(person), viewModel: viewModel)
        view.firstName = "John"
        view.lastName = "Doe"
        view.hasChanges = true
        
        XCTAssertTrue(view.canSave)
    }
    
    func testCannotSaveEditModeWithoutChanges() {
        let person = createTestPerson()
        let view = UnifiedContactView(mode: .edit(person), viewModel: viewModel)
        view.firstName = person.firstName ?? ""
        view.lastName = person.lastName ?? ""
        view.hasChanges = false
        
        XCTAssertFalse(view.canSave)
    }
    
    // MARK: - Helper Methods
    
    private func createTestPerson() -> Person {
        let person = Person(context: testContext)
        person.firstName = "Test"
        person.lastName = "Person"
        person.email = "<EMAIL>"
        person.phoneNumber = "+1234567890"
        person.relationship = "Friend"
        person.notes = "Test notes"
        return person
    }
}

// MARK: - UI Testing Scenarios

/*
 Manual Testing Scenarios:
 
 1. **Add Contact Flow**
    - Tap "+" button in contacts list
    - Fill in contact information
    - Add photo
    - Save contact
    - Verify contact appears in list
 
 2. **View Contact Flow**
    - Tap on existing contact
    - Verify hero section displays correctly
    - Test smart primary action
    - Expand secondary actions
    - Test contact information cards
 
 3. **Edit Contact Flow**
    - From view mode, tap edit button
    - Modify contact information
    - Change photo
    - Save changes
    - Verify changes persist
 
 4. **Mode Transitions**
    - View → Edit: Smooth transition with animations
    - Edit → View: Elements morph back to display mode
    - Cancel editing: Revert to original state
 
 5. **Smart Actions**
    - Contact with phone: Primary action should be "Call"
    - Contact with email only: Primary action should be "Email"
    - Multiple contact methods: Secondary actions revealed
 
 6. **Edge Cases**
    - Contact with no photo
    - Contact with minimal information
    - Very long names/notes
    - Special characters in contact info
 
 7. **Accessibility**
    - VoiceOver navigation
    - Dynamic Type support
    - High contrast mode
    - Reduced motion preferences
 */
