# FirmBond - Personal Relationship Tracker

A beautiful, glassmorphic iOS app for tracking personal relationships and memories.

## ✅ Fixed Provisioning Issues

The app has been updated to work with personal Apple Developer accounts by:

1. **Removed CloudKit dependency** - Changed from `NSPersistentCloudKitContainer` to `NSPersistentContainer`
2. **Removed Push Notifications** - Removed `aps-environment` from entitlements
3. **Updated Bundle Identifier** - Changed to `com.saracini.FirmBond` (compatible with personal accounts)
4. **Simplified Entitlements** - Removed iCloud and CloudKit capabilities

## 🚀 Ready to Run

The app now builds successfully and can be run on:
- iOS Simulator (any device)
- Physical devices with personal Apple Developer account

## 📱 Features

- **Glassmorphic Design** - Beautiful translucent UI with warm colors
- **Contact Management** - Add, edit, and organize personal contacts
- **Search & Filter** - Find contacts by name, relationship, or contact info
- **Relationship Tracking** - Categorize contacts (family, friends, work)
- **Recent Contacts** - Track last contact dates
- **Photo Support** - Add profile photos to contacts
- **Swipe Gestures** - Swipe to delete contacts
- **Haptic Feedback** - Tactile interactions throughout the app

## 🎨 Design System

- **Colors**: Warm creams, muted golds, sage greens
- **Typography**: Large serif fonts for names, monospaced for details
- **Animations**: Smooth spring physics with natural feel
- **Materials**: Ultra-thin glassmorphic effects

## 🏗️ Architecture

- **SwiftUI** - Modern declarative UI framework
- **CoreData** - Local data persistence
- **MVVM** - Clean separation of concerns
- **Reusable Components** - Modular glassmorphic design system

## 🔧 Development

To run the project:
1. Open `FirmBond.xcodeproj` in Xcode
2. Select your development team in project settings
3. Choose a simulator or connected device
4. Build and run (⌘+R)

The app will launch with an empty contacts list. Use the floating action button to add your first contact!
