//
//  DesignSystem.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI

// MARK: - Design System for FirmBond
// Glassmorphic, essentialist design with emotional warmth

struct DesignSystem {
    
    // MARK: - Colors
    struct Colors {
        // Primary warm palette
        static let primaryCream = Color(red: 0.996, green: 0.988, blue: 0.973) // #FEFCF8
        static let secondaryCream = Color(red: 0.973, green: 0.965, blue: 0.949) // #F8F6F2
        static let warmGray = Color(red: 0.909, green: 0.898, blue: 0.886) // #E8E6E2
        static let lightGray = Color(red: 0.831, green: 0.824, blue: 0.808) // #D4D2CE
        
        // Accent colors
        static let mutedGold = Color(red: 0.769, green: 0.647, blue: 0.455) // #C4A574
        static let sageGreen = Color(red: 0.659, green: 0.710, blue: 0.627) // #A8B5A0
        static let softBlue = Color(red: 0.627, green: 0.710, blue: 0.788) // #A0B5C9
        
        // Text colors
        static let richCharcoal = Color(red: 0.173, green: 0.169, blue: 0.161) // #2C2B29
        static let warmBlack = Color(red: 0.102, green: 0.098, blue: 0.094) // #1A1918
        static let subtleText = Color(red: 0.467, green: 0.455, blue: 0.439) // #777470
        
        // Glass effects
        static let glassWhite = Color.white.opacity(0.15)
        static let glassBorder = Color.white.opacity(0.3)
        static let glassShadow = Color.black.opacity(0.08)
    }
    
    // MARK: - Typography
    struct Typography {
        // Serif fonts for emotional warmth
        static let largeTitle = Font.custom("Georgia", size: 32, relativeTo: .largeTitle)
        static let title1 = Font.custom("Georgia", size: 28, relativeTo: .title)
        static let title2 = Font.custom("Georgia", size: 22, relativeTo: .title2)
        static let title3 = Font.custom("Georgia", size: 20, relativeTo: .title3)
        
        // System fonts for readability
        static let headline = Font.system(size: 17, weight: .semibold, design: .default)
        static let body = Font.system(size: 17, weight: .regular, design: .default)
        static let callout = Font.system(size: 16, weight: .regular, design: .default)
        static let subheadline = Font.system(size: 15, weight: .regular, design: .default)
        static let footnote = Font.system(size: 13, weight: .regular, design: .default)
        static let caption = Font.system(size: 12, weight: .regular, design: .default)
        
        // Monospaced for technical details
        static let monoBody = Font.system(size: 17, weight: .regular, design: .monospaced)
        static let monoCallout = Font.system(size: 16, weight: .regular, design: .monospaced)
        static let monoFootnote = Font.system(size: 13, weight: .regular, design: .monospaced)
    }
    
    // MARK: - Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
    }
    
    // MARK: - Corner Radius
    struct CornerRadius {
        static let small: CGFloat = 8
        static let medium: CGFloat = 12
        static let large: CGFloat = 16
        static let extraLarge: CGFloat = 20
        static let round: CGFloat = 50
    }
    
    // MARK: - Shadows
    struct Shadows {
        static let soft = Shadow(
            color: Colors.glassShadow,
            radius: 8,
            x: 0,
            y: 4
        )
        
        static let medium = Shadow(
            color: Colors.glassShadow,
            radius: 12,
            x: 0,
            y: 6
        )
        
        static let large = Shadow(
            color: Colors.glassShadow,
            radius: 20,
            x: 0,
            y: 10
        )
    }
    
    // MARK: - Animations
    struct Animations {
        static let spring = Animation.spring(
            response: 0.6,
            dampingFraction: 0.8,
            blendDuration: 0
        )
        
        static let easeInOut = Animation.easeInOut(duration: 0.3)
        static let gentle = Animation.easeInOut(duration: 0.4)
        static let smooth = Animation.easeInOut(duration: 0.5)
    }
}

// MARK: - Custom Shadow Structure
struct Shadow {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
}

// MARK: - View Extensions for Design System
extension View {
    
    // MARK: - Glassmorphic Effects
    func glassmorphicBackground() -> some View {
        self
            .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large))
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .stroke(DesignSystem.Colors.glassBorder, lineWidth: 1)
            )
    }
    
    func glassmorphicCard() -> some View {
        self
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .fill(DesignSystem.Colors.glassWhite)
                    .background(.ultraThinMaterial)
            )
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .stroke(DesignSystem.Colors.glassBorder, lineWidth: 1)
            )
            .shadow(
                color: DesignSystem.Shadows.soft.color,
                radius: DesignSystem.Shadows.soft.radius,
                x: DesignSystem.Shadows.soft.x,
                y: DesignSystem.Shadows.soft.y
            )
    }
    
    // MARK: - Animations
    func springAnimation() -> some View {
        self.animation(DesignSystem.Animations.spring, value: UUID())
    }
    
    func gentleAnimation() -> some View {
        self.animation(DesignSystem.Animations.gentle, value: UUID())
    }
    
    // MARK: - Haptic Feedback
    func hapticFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle = .medium) -> some View {
        self.onTapGesture {
            let impactFeedback = UIImpactFeedbackGenerator(style: style)
            impactFeedback.impactOccurred()
        }
    }
}
