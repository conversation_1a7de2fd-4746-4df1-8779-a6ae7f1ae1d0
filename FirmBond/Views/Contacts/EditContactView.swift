//
//  EditContactView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import PhotosUI

struct EditContactView: View {
    
    // MARK: - Properties
    let person: Person
    @ObservedObject var viewModel: ContactsViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var firstName: String
    @State private var lastName: String
    @State private var email: String
    @State private var phoneNumber: String
    @State private var relationship: String
    @State private var notes: String
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var photoData: Data?
    @State private var hasChanges = false
    
    @FocusState private var focusedField: Field?
    
    // MARK: - Field Enum
    enum Field {
        case firstName, lastName, email, phone, relationship, notes
    }
    
    // MARK: - Initialization
    init(person: Person, viewModel: ContactsViewModel) {
        self.person = person
        self.viewModel = viewModel
        
        // Initialize state with current person data
        self._firstName = State(initialValue: person.firstName ?? "")
        self._lastName = State(initialValue: person.lastName ?? "")
        self._email = State(initialValue: person.email ?? "")
        self._phoneNumber = State(initialValue: person.phoneNumber ?? "")
        self._relationship = State(initialValue: person.relationship ?? "")
        self._notes = State(initialValue: person.notes ?? "")
        self._photoData = State(initialValue: person.photoData)
    }
    
    // MARK: - Body
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                backgroundGradient
                
                // Content
                ScrollView {
                    VStack(spacing: DesignSystem.Spacing.xl) {
                        // Header
                        headerSection
                        
                        // Photo section
                        photoSection
                        
                        // Form fields
                        formFields
                        
                        // Save button
                        saveButton
                    }
                    .padding(DesignSystem.Spacing.lg)
                }
            }
            .navigationTitle("Edit Contact")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        if hasChanges {
                            // Show confirmation dialog
                        } else {
                            dismiss()
                        }
                    }
                    .foregroundColor(DesignSystem.Colors.subtleText)
                }
            }
        }
        .onChange(of: selectedPhoto) { _, newPhoto in
            Task {
                if let newPhoto = newPhoto,
                   let data = try? await newPhoto.loadTransferable(type: Data.self) {
                    photoData = data
                    hasChanges = true
                }
            }
        }
        .onChange(of: firstName) { _, _ in hasChanges = true }
        .onChange(of: lastName) { _, _ in hasChanges = true }
        .onChange(of: email) { _, _ in hasChanges = true }
        .onChange(of: phoneNumber) { _, _ in hasChanges = true }
        .onChange(of: relationship) { _, _ in hasChanges = true }
        .onChange(of: notes) { _, _ in hasChanges = true }
    }
    
    // MARK: - Private Views
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                DesignSystem.Colors.primaryCream,
                DesignSystem.Colors.secondaryCream
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    private var headerSection: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            Text("Edit Contact")
                .font(DesignSystem.Typography.largeTitle)
                .foregroundColor(DesignSystem.Colors.warmBlack)
            
            Text("Update contact information")
                .font(DesignSystem.Typography.callout)
                .foregroundColor(DesignSystem.Colors.subtleText)
        }
    }
    
    private var photoSection: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // Photo picker
            PhotosPicker(
                selection: $selectedPhoto,
                matching: .images,
                photoLibrary: .shared()
            ) {
                ZStack {
                    if let photoData = photoData,
                       let uiImage = UIImage(data: photoData) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 140, height: 140)
                            .clipShape(Circle())
                    } else {
                        Circle()
                            .fill(DesignSystem.Colors.mutedGold.opacity(0.3))
                            .frame(width: 140, height: 140)
                            .overlay(
                                VStack(spacing: DesignSystem.Spacing.xs) {
                                    Image(systemName: "camera.fill")
                                        .font(.title)
                                        .foregroundColor(DesignSystem.Colors.mutedGold)
                                    
                                    Text("Edit Photo")
                                        .font(DesignSystem.Typography.caption)
                                        .foregroundColor(DesignSystem.Colors.subtleText)
                                }
                            )
                    }
                    
                    // Edit overlay
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Circle()
                                .fill(DesignSystem.Colors.mutedGold)
                                .frame(width: 32, height: 32)
                                .overlay(
                                    Image(systemName: "pencil")
                                        .font(.caption)
                                        .foregroundColor(.white)
                                )
                                .offset(x: -10, y: -10)
                        }
                    }
                    .frame(width: 140, height: 140)
                }
                .overlay(
                    Circle()
                        .stroke(DesignSystem.Colors.glassBorder, lineWidth: 2)
                )
                .shadow(
                    color: DesignSystem.Colors.glassShadow,
                    radius: 8,
                    x: 0,
                    y: 4
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    private var formFields: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // Name fields
            HStack(spacing: DesignSystem.Spacing.md) {
                ModernFormField(
                    title: "First Name",
                    text: $firstName,
                    placeholder: "John",
                    isRequired: true
                )
                .focused($focusedField, equals: .firstName)
                
                ModernFormField(
                    title: "Last Name",
                    text: $lastName,
                    placeholder: "Doe",
                    isRequired: true
                )
                .focused($focusedField, equals: .lastName)
            }
            
            // Contact information
            ModernFormField(
                title: "Email",
                text: $email,
                placeholder: "<EMAIL>",
                keyboardType: .emailAddress,
                icon: "envelope"
            )
            .focused($focusedField, equals: .email)
            
            ModernFormField(
                title: "Phone",
                text: $phoneNumber,
                placeholder: "(*************",
                keyboardType: .phonePad,
                icon: "phone"
            )
            .focused($focusedField, equals: .phone)
            
            // Relationship
            ModernFormField(
                title: "Relationship",
                text: $relationship,
                placeholder: "Friend, Colleague, Family...",
                icon: "person.2"
            )
            .focused($focusedField, equals: .relationship)
            
            // Notes
            ModernFormField(
                title: "Notes",
                text: $notes,
                placeholder: "Additional notes...",
                isMultiline: true,
                icon: "note.text"
            )
            .focused($focusedField, equals: .notes)
        }
    }
    
    private var saveButton: some View {
        Button(action: saveChanges) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                Image(systemName: "checkmark.circle.fill")
                Text("Save Changes")
            }
            .font(DesignSystem.Typography.headline)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .fill(
                        canSave ? DesignSystem.Colors.mutedGold : DesignSystem.Colors.lightGray
                    )
            )
        }
        .disabled(!canSave)
        .buttonStyle(PlainButtonStyle())
        .animation(DesignSystem.Animations.gentle, value: canSave)
    }
    
    // MARK: - Computed Properties
    private var canSave: Bool {
        !firstName.trimmingCharacters(in: .whitespaces).isEmpty &&
        !lastName.trimmingCharacters(in: .whitespaces).isEmpty &&
        hasChanges
    }
    
    // MARK: - Private Methods
    private func saveChanges() {
        guard canSave else { return }
        
        viewModel.updatePerson(
            person,
            firstName: firstName,
            lastName: lastName,
            email: email.isEmpty ? nil : email,
            phoneNumber: phoneNumber.isEmpty ? nil : phoneNumber,
            relationship: relationship.isEmpty ? nil : relationship,
            notes: notes.isEmpty ? nil : notes,
            photoData: photoData
        )
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        dismiss()
    }
}

// MARK: - Modern Form Field Component
struct ModernFormField: View {
    let title: String
    @Binding var text: String
    let placeholder: String
    let keyboardType: UIKeyboardType
    let isRequired: Bool
    let isMultiline: Bool
    let icon: String?
    
    @FocusState private var isFocused: Bool
    
    init(
        title: String,
        text: Binding<String>,
        placeholder: String,
        keyboardType: UIKeyboardType = .default,
        isRequired: Bool = false,
        isMultiline: Bool = false,
        icon: String? = nil
    ) {
        self.title = title
        self._text = text
        self.placeholder = placeholder
        self.keyboardType = keyboardType
        self.isRequired = isRequired
        self.isMultiline = isMultiline
        self.icon = icon
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
            HStack {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.caption)
                        .foregroundColor(DesignSystem.Colors.mutedGold)
                        .frame(width: 16)
                }
                
                Text(title)
                    .font(DesignSystem.Typography.callout)
                    .fontWeight(.medium)
                    .foregroundColor(DesignSystem.Colors.warmBlack)
                
                if isRequired {
                    Text("*")
                        .font(DesignSystem.Typography.callout)
                        .foregroundColor(.red)
                }
                
                Spacer()
            }
            
            GlassmorphicCard(
                cornerRadius: DesignSystem.CornerRadius.medium,
                padding: DesignSystem.Spacing.md,
                shadowStyle: isFocused ? .medium : .soft
            ) {
                if isMultiline {
                    TextField(placeholder, text: $text, axis: .vertical)
                        .lineLimit(3...6)
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                        .keyboardType(keyboardType)
                        .focused($isFocused)
                } else {
                    TextField(placeholder, text: $text)
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                        .keyboardType(keyboardType)
                        .focused($isFocused)
                }
            }
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .stroke(
                        isFocused ? DesignSystem.Colors.mutedGold : Color.clear,
                        lineWidth: 2
                    )
            )
            .animation(DesignSystem.Animations.gentle, value: isFocused)
        }
    }
}
