//
//  AddContactView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import PhotosUI

struct AddContactView: View {
    
    // MARK: - Properties
    @ObservedObject var viewModel: ContactsViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var firstName = ""
    @State private var lastName = ""
    @State private var email = ""
    @State private var phoneNumber = ""
    @State private var relationship = ""
    @State private var notes = ""
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var photoData: Data?
    
    @FocusState private var focusedField: Field?
    
    // MARK: - Field Enum
    enum Field {
        case firstName, lastName, email, phone, relationship, notes
    }
    
    // MARK: - Body
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                backgroundGradient
                
                // Content
                ScrollView {
                    VStack(spacing: DesignSystem.Spacing.xl) {
                        // Header
                        headerSection
                        
                        // Photo section
                        photoSection
                        
                        // Form fields
                        formFields
                        
                        // Save button
                        saveButton
                    }
                    .padding(DesignSystem.Spacing.lg)
                }
            }
            .navigationTitle("New Contact")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.subtleText)
                }
            }
        }
        .onChange(of: selectedPhoto) { _, newPhoto in
            Task {
                if let newPhoto = newPhoto,
                   let data = try? await newPhoto.loadTransferable(type: Data.self) {
                    photoData = data
                }
            }
        }
    }
    
    // MARK: - Private Views
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                DesignSystem.Colors.primaryCream,
                DesignSystem.Colors.secondaryCream
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    private var headerSection: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            Text("Add New Contact")
                .font(DesignSystem.Typography.largeTitle)
                .foregroundColor(DesignSystem.Colors.warmBlack)
            
            Text("Build your personal network")
                .font(DesignSystem.Typography.callout)
                .foregroundColor(DesignSystem.Colors.subtleText)
        }
    }
    
    private var photoSection: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // Photo picker
            PhotosPicker(
                selection: $selectedPhoto,
                matching: .images,
                photoLibrary: .shared()
            ) {
                ZStack {
                    if let photoData = photoData,
                       let uiImage = UIImage(data: photoData) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 120, height: 120)
                            .clipShape(Circle())
                    } else {
                        Circle()
                            .fill(DesignSystem.Colors.mutedGold.opacity(0.3))
                            .frame(width: 120, height: 120)
                            .overlay(
                                VStack(spacing: DesignSystem.Spacing.xs) {
                                    Image(systemName: "camera")
                                        .font(.title2)
                                        .foregroundColor(DesignSystem.Colors.mutedGold)
                                    
                                    Text("Add Photo")
                                        .font(DesignSystem.Typography.caption)
                                        .foregroundColor(DesignSystem.Colors.subtleText)
                                }
                            )
                    }
                }
                .overlay(
                    Circle()
                        .stroke(DesignSystem.Colors.glassBorder, lineWidth: 2)
                )
                .shadow(
                    color: DesignSystem.Colors.glassShadow,
                    radius: 8,
                    x: 0,
                    y: 4
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    private var formFields: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // Name fields
            HStack(spacing: DesignSystem.Spacing.md) {
                FormField(
                    title: "First Name",
                    text: $firstName,
                    placeholder: "John",
                    isRequired: true
                )
                .focused($focusedField, equals: .firstName)
                
                FormField(
                    title: "Last Name",
                    text: $lastName,
                    placeholder: "Doe",
                    isRequired: true
                )
                .focused($focusedField, equals: .lastName)
            }
            
            // Contact information
            FormField(
                title: "Email",
                text: $email,
                placeholder: "<EMAIL>",
                keyboardType: .emailAddress
            )
            .focused($focusedField, equals: .email)
            
            FormField(
                title: "Phone",
                text: $phoneNumber,
                placeholder: "(*************",
                keyboardType: .phonePad
            )
            .focused($focusedField, equals: .phone)
            
            // Relationship
            FormField(
                title: "Relationship",
                text: $relationship,
                placeholder: "Friend, Colleague, Family..."
            )
            .focused($focusedField, equals: .relationship)
            
            // Notes
            FormField(
                title: "Notes",
                text: $notes,
                placeholder: "Additional notes...",
                isMultiline: true
            )
            .focused($focusedField, equals: .notes)
        }
    }
    
    private var saveButton: some View {
        Button(action: saveContact) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                Image(systemName: "person.badge.plus")
                Text("Save Contact")
            }
            .font(DesignSystem.Typography.headline)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .fill(
                        canSave ? DesignSystem.Colors.mutedGold : DesignSystem.Colors.lightGray
                    )
            )
        }
        .disabled(!canSave)
        .buttonStyle(PlainButtonStyle())
        .animation(DesignSystem.Animations.gentle, value: canSave)
    }
    
    // MARK: - Computed Properties
    private var canSave: Bool {
        !firstName.trimmingCharacters(in: .whitespaces).isEmpty &&
        !lastName.trimmingCharacters(in: .whitespaces).isEmpty
    }
    
    // MARK: - Private Methods
    private func saveContact() {
        guard canSave else { return }
        
        viewModel.addPerson(
            firstName: firstName,
            lastName: lastName,
            email: email.isEmpty ? nil : email,
            phoneNumber: phoneNumber.isEmpty ? nil : phoneNumber,
            relationship: relationship.isEmpty ? nil : relationship,
            notes: notes.isEmpty ? nil : notes,
            photoData: photoData
        )
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        dismiss()
    }
}

// MARK: - Form Field Component
struct FormField: View {
    let title: String
    @Binding var text: String
    let placeholder: String
    let keyboardType: UIKeyboardType
    let isRequired: Bool
    let isMultiline: Bool
    
    init(
        title: String,
        text: Binding<String>,
        placeholder: String,
        keyboardType: UIKeyboardType = .default,
        isRequired: Bool = false,
        isMultiline: Bool = false
    ) {
        self.title = title
        self._text = text
        self.placeholder = placeholder
        self.keyboardType = keyboardType
        self.isRequired = isRequired
        self.isMultiline = isMultiline
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
            HStack {
                Text(title)
                    .font(DesignSystem.Typography.callout)
                    .fontWeight(.medium)
                    .foregroundColor(DesignSystem.Colors.warmBlack)
                
                if isRequired {
                    Text("*")
                        .font(DesignSystem.Typography.callout)
                        .foregroundColor(.red)
                }
                
                Spacer()
            }
            
            GlassmorphicCard(
                cornerRadius: DesignSystem.CornerRadius.medium,
                padding: DesignSystem.Spacing.sm,
                shadowStyle: .none
            ) {
                if isMultiline {
                    TextField(placeholder, text: $text, axis: .vertical)
                        .lineLimit(3...6)
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                        .keyboardType(keyboardType)
                } else {
                    TextField(placeholder, text: $text)
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                        .keyboardType(keyboardType)
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    AddContactView(viewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext))
}
