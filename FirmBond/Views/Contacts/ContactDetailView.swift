//
//  ContactDetailView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import MessageUI

struct ContactDetailView: View {

    // MARK: - Properties
    let person: Person
    @ObservedObject var viewModel: ContactsViewModel
    @Environment(\.dismiss) private var dismiss

    @State private var isShowingEditView = false
    @State private var showingDeleteConfirmation = false
    @State private var showingActionSheet = false
    @State private var scrollOffset: CGFloat = 0
    @State private var isHeaderVisible = true
    @State private var showingSecondaryActions = false
    @State private var primaryActionScale: CGFloat = 1.0
    @State private var isPerformingAction = false
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // Background
            backgroundGradient

            // Main content
            ScrollView {
                VStack(spacing: 0) {
                    // Hero section with photo
                    heroSection
                        .frame(height: 350)

                    // Content cards
                    VStack(spacing: DesignSystem.Spacing.lg) {
                        // Smart primary action
                        smartPrimaryActionSection

                        // Secondary actions (revealed on demand)
                        if showingSecondaryActions {
                            secondaryActionsSection
                                .transition(.asymmetric(
                                    insertion: .move(edge: .top).combined(with: .opacity),
                                    removal: .move(edge: .top).combined(with: .opacity)
                                ))
                        }

                        // Contact information cards
                        contactInformationCards

                        // Notes card
                        if let notes = person.notes, !notes.isEmpty {
                            notesCard
                        }

                        // Activity card
                        activityCard

                        // Bottom padding for floating button
                        Spacer()
                            .frame(height: 100)
                    }
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    .padding(.top, DesignSystem.Spacing.lg)
                    .background(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.extraLarge)
                            .fill(DesignSystem.Colors.primaryCream)
                            .shadow(
                                color: DesignSystem.Colors.glassShadow,
                                radius: 20,
                                x: 0,
                                y: -10
                            )
                    )
                    .offset(y: -DesignSystem.Spacing.xl)
                }
            }
            .ignoresSafeArea(edges: .top)

            // Floating header
            floatingHeader

            // Quick action buttons
            quickActionButtons
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $isShowingEditView) {
            EditContactView(person: person, viewModel: viewModel)
        }
        .confirmationDialog(
            "Delete Contact",
            isPresented: $showingDeleteConfirmation,
            titleVisibility: .visible
        ) {
            Button("Delete", role: .destructive) {
                viewModel.deletePerson(person)
                dismiss()
            }
            Button("Cancel", role: .cancel) {}
        } message: {
            Text("Are you sure you want to delete \(person.fullName)? This action cannot be undone.")
        }
        .actionSheet(isPresented: $showingActionSheet) {
            ActionSheet(
                title: Text(person.fullName),
                buttons: [
                    .default(Text("Edit Contact")) {
                        isShowingEditView = true
                    },
                    .default(Text("Share Contact")) {
                        shareContact()
                    },
                    .destructive(Text("Delete Contact")) {
                        showingDeleteConfirmation = true
                    },
                    .cancel()
                ]
            )
        }
    }
    
    // MARK: - Private Views
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                DesignSystem.Colors.primaryCream,
                DesignSystem.Colors.secondaryCream,
                DesignSystem.Colors.warmGray.opacity(0.3)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }

    private var heroSection: some View {
        ZStack {
            // Background with photo or gradient
            Group {
                if let photoData = person.photoData,
                   let uiImage = UIImage(data: photoData) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .clipped()
                } else {
                    LinearGradient(
                        colors: [
                            DesignSystem.Colors.mutedGold,
                            DesignSystem.Colors.sageGreen
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                }
            }

            // Gradient overlay
            LinearGradient(
                colors: [
                    Color.clear,
                    Color.black.opacity(0.7)
                ],
                startPoint: .center,
                endPoint: .bottom
            )

            // Content - centered
            VStack(spacing: DesignSystem.Spacing.lg) {
                Spacer()

                // Profile photo (circular overlay for no-photo contacts)
                if person.photoData == nil {
                    Circle()
                        .fill(.ultraThinMaterial)
                        .frame(width: 120, height: 120)
                        .overlay(
                            Text(person.initials)
                                .font(.system(size: 42, weight: .bold))
                                .foregroundColor(DesignSystem.Colors.warmBlack)
                        )
                        .shadow(
                            color: Color.black.opacity(0.3),
                            radius: 15,
                            x: 0,
                            y: 8
                        )
                }

                // Name and relationship - centered
                VStack(spacing: DesignSystem.Spacing.sm) {
                    Text(person.fullName)
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .shadow(color: .black.opacity(0.5), radius: 3, x: 0, y: 2)

                    if let relationship = person.relationship, !relationship.isEmpty {
                        Text(relationship)
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white.opacity(0.9))
                            .multilineTextAlignment(.center)
                            .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                    }
                }

                Spacer()
                    .frame(height: DesignSystem.Spacing.xxl)
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
        }
    }

    private var floatingHeader: some View {
        VStack {
            HStack {
                Button(action: { dismiss() }) {
                    Circle()
                        .fill(.ultraThinMaterial)
                        .frame(width: 44, height: 44)
                        .overlay(
                            Image(systemName: "chevron.left")
                                .font(.title3)
                                .fontWeight(.semibold)
                                .foregroundColor(DesignSystem.Colors.warmBlack)
                        )
                }

                Spacer()

                Button(action: { isShowingEditView = true }) {
                    Circle()
                        .fill(.ultraThinMaterial)
                        .frame(width: 44, height: 44)
                        .overlay(
                            Image(systemName: "pencil")
                                .font(.title3)
                                .fontWeight(.semibold)
                                .foregroundColor(DesignSystem.Colors.warmBlack)
                        )
                }

                Button(action: { showingActionSheet = true }) {
                    Circle()
                        .fill(.ultraThinMaterial)
                        .frame(width: 44, height: 44)
                        .overlay(
                            Image(systemName: "ellipsis")
                                .font(.title3)
                                .fontWeight(.semibold)
                                .foregroundColor(DesignSystem.Colors.warmBlack)
                        )
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.top, DesignSystem.Spacing.md)

            Spacer()
        }
    }

    // MARK: - Smart Primary Action (Neuropsychology: Hick's Law)
    private var smartPrimaryActionSection: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // Primary action - determined by smart algorithm
            if let primaryAction = getPrimaryAction() {
                SmartPrimaryActionButton(
                    action: primaryAction,
                    isPerforming: $isPerformingAction,
                    scale: $primaryActionScale
                ) {
                    performPrimaryAction(primaryAction)
                }
            }

            // "More actions" hint (Progressive Disclosure)
            if hasSecondaryActions() {
                Button(action: {
                    withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                        showingSecondaryActions.toggle()
                    }
                }) {
                    HStack(spacing: DesignSystem.Spacing.xs) {
                        Text(showingSecondaryActions ? "Less" : "More actions")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.mutedGold)

                        Image(systemName: showingSecondaryActions ? "chevron.up" : "chevron.down")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.mutedGold)
                    }
                    .padding(.vertical, DesignSystem.Spacing.xs)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }

    // Secondary actions (Progressive Disclosure)
    private var secondaryActionsSection: some View {
        let secondaryActions = getSecondaryActions()

        return VStack(spacing: DesignSystem.Spacing.sm) {
            ForEach(secondaryActions, id: \.title) { action in
                SecondaryActionButton(action: action) {
                    performAction(action)
                }
            }
        }
        .padding(.top, DesignSystem.Spacing.sm)
    }

    // MARK: - Smart Action Detection (Neuropsychology: Decision Making)

    // Primary action based on smart algorithm (Hick's Law - reduce choices)
    private func getPrimaryAction() -> QuickAction? {
        // Priority: Phone > Message > Email (based on immediacy and emotional connection)
        if let phoneNumber = person.phoneNumber, !phoneNumber.isEmpty {
            return QuickAction(
                icon: "phone.fill",
                title: "Call \(person.firstName ?? "Contact")",
                color: DesignSystem.Colors.sageGreen,
                action: {
                    if let url = URL(string: "tel://\(phoneNumber)") {
                        UIApplication.shared.open(url)
                    }
                }
            )
        }

        if let phoneNumber = person.phoneNumber, !phoneNumber.isEmpty {
            return QuickAction(
                icon: "message.fill",
                title: "Message \(person.firstName ?? "Contact")",
                color: DesignSystem.Colors.softBlue,
                action: {
                    if let url = URL(string: "sms://\(phoneNumber)") {
                        UIApplication.shared.open(url)
                    }
                }
            )
        }

        if let email = person.email, !email.isEmpty {
            return QuickAction(
                icon: "envelope.fill",
                title: "Email \(person.firstName ?? "Contact")",
                color: DesignSystem.Colors.mutedGold,
                action: {
                    if let url = URL(string: "mailto:\(email)") {
                        UIApplication.shared.open(url)
                    }
                }
            )
        }

        return nil
    }

    // Secondary actions (Progressive Disclosure)
    private func getSecondaryActions() -> [QuickAction] {
        var actions: [QuickAction] = []
        let primaryAction = getPrimaryAction()

        // Add all available actions except the primary one
        if let phoneNumber = person.phoneNumber, !phoneNumber.isEmpty {
            if primaryAction?.icon != "phone.fill" {
                actions.append(QuickAction(
                    icon: "phone.fill",
                    title: "Call",
                    color: DesignSystem.Colors.sageGreen,
                    action: {
                        if let url = URL(string: "tel://\(phoneNumber)") {
                            UIApplication.shared.open(url)
                        }
                    }
                ))
            }

            if primaryAction?.icon != "message.fill" {
                actions.append(QuickAction(
                    icon: "message.fill",
                    title: "Message",
                    color: DesignSystem.Colors.softBlue,
                    action: {
                        if let url = URL(string: "sms://\(phoneNumber)") {
                            UIApplication.shared.open(url)
                        }
                    }
                ))
            }
        }

        if let email = person.email, !email.isEmpty, primaryAction?.icon != "envelope.fill" {
            actions.append(QuickAction(
                icon: "envelope.fill",
                title: "Email",
                color: DesignSystem.Colors.mutedGold,
                action: {
                    if let url = URL(string: "mailto:\(email)") {
                        UIApplication.shared.open(url)
                    }
                }
            ))
        }

        return actions
    }

    private func hasSecondaryActions() -> Bool {
        return !getSecondaryActions().isEmpty
    }

    private func performPrimaryAction(_ action: QuickAction) {
        // Haptic feedback for primary action
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        // Visual feedback
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
            primaryActionScale = 0.95
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                primaryActionScale = 1.0
            }
        }

        // Perform action
        action.action()
    }

    private func performAction(_ action: QuickAction) {
        // Lighter haptic for secondary actions
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        action.action()
    }

    private var quickActionButtons: some View {
        VStack {
            Spacer()

            HStack {
                Spacer()

                VStack(spacing: DesignSystem.Spacing.sm) {
                    // Primary action based on available contact methods
                    if let phoneNumber = person.phoneNumber, !phoneNumber.isEmpty {
                        FloatingActionButton(
                            icon: "phone.fill",
                            color: DesignSystem.Colors.sageGreen
                        ) {
                            if let url = URL(string: "tel://\(phoneNumber)") {
                                UIApplication.shared.open(url)
                            }
                        }
                    } else if let email = person.email, !email.isEmpty {
                        FloatingActionButton(
                            icon: "envelope.fill",
                            color: DesignSystem.Colors.mutedGold
                        ) {
                            if let url = URL(string: "mailto:\(email)") {
                                UIApplication.shared.open(url)
                            }
                        }
                    }
                }
                .padding(.trailing, DesignSystem.Spacing.lg)
                .padding(.bottom, DesignSystem.Spacing.xl)
            }
        }
    }

    private var contactInformationCards: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // Phone card
            if let phoneNumber = person.phoneNumber, !phoneNumber.isEmpty {
                ContactInfoCard(
                    icon: "phone",
                    title: "Phone",
                    value: person.displayPhoneNumber,
                    color: DesignSystem.Colors.sageGreen
                ) {
                    if let url = URL(string: "tel://\(phoneNumber)") {
                        UIApplication.shared.open(url)
                    }
                }
            }

            // Email card
            if let email = person.email, !email.isEmpty {
                ContactInfoCard(
                    icon: "envelope",
                    title: "Email",
                    value: email,
                    color: DesignSystem.Colors.mutedGold
                ) {
                    if let url = URL(string: "mailto:\(email)") {
                        UIApplication.shared.open(url)
                    }
                }
            }

            // Relationship card
            if let relationship = person.relationship, !relationship.isEmpty {
                ContactInfoCard(
                    icon: "person.2",
                    title: "Relationship",
                    value: relationship,
                    color: DesignSystem.Colors.softBlue
                )
            }
        }
    }

    private var notesCard: some View {
        GlassmorphicCard {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                HStack {
                    Image(systemName: "note.text")
                        .font(DesignSystem.Typography.title3)
                        .foregroundColor(DesignSystem.Colors.mutedGold)

                    Text("Notes")
                        .font(DesignSystem.Typography.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(DesignSystem.Colors.warmBlack)

                    Spacer()
                }

                Text(person.notes ?? "")
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.warmBlack)
                    .lineSpacing(4)
            }
        }
    }

    private var activityCard: some View {
        GlassmorphicCard {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                HStack {
                    Image(systemName: "clock")
                        .font(DesignSystem.Typography.title3)
                        .foregroundColor(DesignSystem.Colors.softBlue)

                    Text("Activity")
                        .font(DesignSystem.Typography.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(DesignSystem.Colors.warmBlack)

                    Spacer()
                }

                VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                    if let dateAdded = person.dateAdded {
                        ActivityRow(
                            icon: "plus.circle",
                            title: "Contact Added",
                            date: dateAdded,
                            color: DesignSystem.Colors.sageGreen
                        )
                    }

                    if let lastContact = person.lastContactDate {
                        ActivityRow(
                            icon: "phone",
                            title: "Last Contact",
                            date: lastContact,
                            color: DesignSystem.Colors.mutedGold
                        )
                    } else {
                        HStack {
                            Image(systemName: "exclamationmark.circle")
                                .foregroundColor(DesignSystem.Colors.subtleText)

                            Text("No recent contact")
                                .font(DesignSystem.Typography.callout)
                                .foregroundColor(DesignSystem.Colors.subtleText)
                        }
                    }
                }
            }
        }
    }

    // MARK: - Private Methods
    private func shareContact() {
        // Implement contact sharing functionality
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    

    

    

    

    

}

// MARK: - Supporting Structures
struct QuickAction {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void
}

// MARK: - Neuropsychology-Optimized Components

// Primary action button (Fitts's Law - larger target, easier to hit)
struct SmartPrimaryActionButton: View {
    let action: QuickAction
    @Binding var isPerforming: Bool
    @Binding var scale: CGFloat
    let onTap: () -> Void

    @State private var isPressed = false

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: DesignSystem.Spacing.md) {
                Circle()
                    .fill(action.color)
                    .frame(width: 56, height: 56)
                    .overlay(
                        Image(systemName: action.icon)
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(.white)
                    )
                    .shadow(
                        color: action.color.opacity(0.4),
                        radius: 8,
                        x: 0,
                        y: 4
                    )

                VStack(alignment: .leading, spacing: 4) {
                    Text(action.title)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.warmBlack)

                    Text("Primary action")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.subtleText)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.subtleText)
            }
            .padding(DesignSystem.Spacing.lg)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                            .stroke(action.color.opacity(0.3), lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(scale)
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        } perform: {}
    }
}

// Secondary action button (smaller, less prominent)
struct SecondaryActionButton: View {
    let action: QuickAction
    let onTap: () -> Void

    @State private var isPressed = false

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: DesignSystem.Spacing.md) {
                Circle()
                    .fill(action.color.opacity(0.15))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Image(systemName: action.icon)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(action.color)
                    )

                Text(action.title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.warmBlack)

                Spacer()
            }
            .padding(DesignSystem.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .fill(DesignSystem.Colors.primaryCream.opacity(0.5))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.96 : 1.0)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        } perform: {}
    }
}

// MARK: - Supporting Views
struct QuickActionCard: View {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void

    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            VStack(spacing: DesignSystem.Spacing.sm) {
                Circle()
                    .fill(color.opacity(0.15))
                    .frame(width: 56, height: 56)
                    .overlay(
                        Image(systemName: icon)
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(color)
                    )

                Text(title)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.warmBlack)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 100)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                            .stroke(color.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(DesignSystem.Animations.easeInOut) {
                isPressed = pressing
            }

            // Haptic feedback
            if pressing {
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }
        } perform: {}
    }
}

struct ContactInfoCard: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    let action: (() -> Void)?

    init(icon: String, title: String, value: String, color: Color, action: (() -> Void)? = nil) {
        self.icon = icon
        self.title = title
        self.value = value
        self.color = color
        self.action = action
    }

    var body: some View {
        Button(action: action ?? {}) {
            GlassmorphicCard {
                HStack(spacing: DesignSystem.Spacing.md) {
                    Circle()
                        .fill(color.opacity(0.15))
                        .frame(width: 44, height: 44)
                        .overlay(
                            Image(systemName: icon)
                                .font(.title3)
                                .foregroundColor(color)
                        )

                    VStack(alignment: .leading, spacing: 4) {
                        Text(title)
                            .font(DesignSystem.Typography.caption)
                            .fontWeight(.medium)
                            .foregroundColor(DesignSystem.Colors.subtleText)

                        Text(value)
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.warmBlack)
                            .lineLimit(2)
                    }

                    Spacer()

                    if action != nil {
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(DesignSystem.Colors.subtleText)
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(action == nil)
    }
}

struct FloatingActionButton: View {
    let icon: String
    let color: Color
    let action: () -> Void

    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            Circle()
                .fill(color)
                .frame(width: 56, height: 56)
                .overlay(
                    Image(systemName: icon)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                )
                .shadow(
                    color: color.opacity(0.4),
                    radius: 8,
                    x: 0,
                    y: 4
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.9 : 1.0)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(DesignSystem.Animations.spring) {
                isPressed = pressing
            }
        } perform: {}
    }
}

struct ActivityRow: View {
    let icon: String
    let title: String
    let date: Date
    let color: Color

    var body: some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            Circle()
                .fill(color.opacity(0.15))
                .frame(width: 32, height: 32)
                .overlay(
                    Image(systemName: icon)
                        .font(.caption)
                        .foregroundColor(color)
                )

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(DesignSystem.Typography.callout)
                    .foregroundColor(DesignSystem.Colors.warmBlack)

                Text(DateFormatter.contactDate.string(from: date))
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.subtleText)
            }

            Spacer()
        }
    }
}

// MARK: - Date Formatter Extension
extension DateFormatter {
    static let contactDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter
    }()
}

// MARK: - Preview
#Preview {
    let samplePerson = Person(context: PersistenceController.preview.container.viewContext)
    samplePerson.firstName = "John"
    samplePerson.lastName = "Doe"
    samplePerson.email = "<EMAIL>"
    samplePerson.phoneNumber = "1234567890"
    samplePerson.relationship = "Friend"
    samplePerson.notes = "Met at the conference last year. Really interested in sustainable technology."
    samplePerson.dateAdded = Date()
    samplePerson.lastContactDate = Date()
    
    return ContactDetailView(
        person: samplePerson,
        viewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext)
    )
}
