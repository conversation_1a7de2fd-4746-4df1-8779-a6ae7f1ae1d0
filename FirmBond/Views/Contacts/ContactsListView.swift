//
//  ContactsListView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import CoreData

struct ContactsListView: View {
    
    // MARK: - Properties
    @StateObject private var viewModel: ContactsViewModel
    @State private var selectedFilter: EnhancedContactSearchBar.ContactFilter = .all
    @State private var showingAddContact = false
    @State private var selectedPerson: Person?
    
    // MARK: - Initialization
    init(viewContext: NSManagedObjectContext) {
        self._viewModel = StateObject(wrappedValue: ContactsViewModel(viewContext: viewContext))
    }
    
    // MARK: - Body
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                backgroundGradient
                
                // Main content
                VStack(spacing: 0) {
                    // Header
                    headerView
                    
                    // Search and filters
                    searchSection
                    
                    // Content
                    if viewModel.isLoading {
                        loadingView
                    } else if viewModel.filteredPeople.isEmpty {
                        emptyStateView
                    } else {
                        contactsList
                    }
                }
                
                // Floating add button
                floatingAddButton
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
        .sheet(isPresented: $showingAddContact) {
            UnifiedContactView(mode: .add, viewModel: viewModel)
        }
        .sheet(item: $selectedPerson) { person in
            UnifiedContactView(mode: .view(person), viewModel: viewModel)
        }
        .onAppear {
            viewModel.fetchPeople()
        }
        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("OK") {
                viewModel.errorMessage = nil
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
    }
    
    // MARK: - Private Views
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                DesignSystem.Colors.primaryCream,
                DesignSystem.Colors.secondaryCream,
                DesignSystem.Colors.warmGray.opacity(0.3)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    private var headerView: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Contacts")
                        .font(DesignSystem.Typography.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                    
                    Text("\(viewModel.filteredPeople.count) people")
                        .font(DesignSystem.Typography.callout)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                }
                
                Spacer()
            }
            
            // Recent contacts section
            if !viewModel.recentContacts.isEmpty && selectedFilter == .all && viewModel.searchText.isEmpty {
                recentContactsSection
            }
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.top, DesignSystem.Spacing.md)
    }
    
    private var recentContactsSection: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            Text("Recent")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.warmBlack)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: DesignSystem.Spacing.sm) {
                    ForEach(viewModel.recentContacts, id: \.id) { person in
                        PersonBubbleCompactView(person: person) {
                            selectedPerson = person
                        }
                        .frame(width: 120)
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
            }
        }
    }
    
    private var searchSection: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            EnhancedContactSearchBar(
                searchText: $viewModel.searchText,
                selectedFilter: $selectedFilter
            )
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.vertical, DesignSystem.Spacing.md)
    }
    
    private var contactsList: some View {
        ScrollView {
            LazyVStack(spacing: DesignSystem.Spacing.md) {
                ForEach(filteredContacts, id: \.id) { person in
                    PersonBubbleView(
                        person: person,
                        isSelected: selectedPerson?.id == person.id,
                        onTap: {
                            selectedPerson = person
                        },
                        onDelete: {
                            viewModel.deletePerson(person)
                        }
                    )
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.bottom, 100) // Space for floating button
        }
        .animation(DesignSystem.Animations.spring, value: viewModel.filteredPeople)
    }
    
    private var loadingView: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(DesignSystem.Colors.mutedGold)
            
            Text("Loading contacts...")
                .font(DesignSystem.Typography.callout)
                .foregroundColor(DesignSystem.Colors.subtleText)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: DesignSystem.Spacing.xl) {
            Image(systemName: "person.2.circle")
                .font(.system(size: 64))
                .foregroundColor(DesignSystem.Colors.subtleText)
            
            VStack(spacing: DesignSystem.Spacing.sm) {
                Text(emptyStateTitle)
                    .font(DesignSystem.Typography.title2)
                    .foregroundColor(DesignSystem.Colors.warmBlack)
                    .multilineTextAlignment(.center)
                
                Text(emptyStateMessage)
                    .font(DesignSystem.Typography.callout)
                    .foregroundColor(DesignSystem.Colors.subtleText)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: { showingAddContact = true }) {
                HStack(spacing: DesignSystem.Spacing.sm) {
                    Image(systemName: "plus")
                    Text("Add Your First Contact")
                }
                .font(DesignSystem.Typography.headline)
                .foregroundColor(.white)
                .padding(.horizontal, DesignSystem.Spacing.xl)
                .padding(.vertical, DesignSystem.Spacing.md)
                .background(
                    RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                        .fill(DesignSystem.Colors.mutedGold)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(DesignSystem.Spacing.xl)
    }
    
    private var floatingAddButton: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()
                
                FloatingActionCard {
                    showingAddContact = true
                } content: {
                    Image(systemName: "plus")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                }
                .padding(.trailing, DesignSystem.Spacing.lg)
                .padding(.bottom, DesignSystem.Spacing.xl)
            }
        }
    }
    
    // MARK: - Computed Properties
    private var filteredContacts: [Person] {
        let baseContacts = viewModel.filteredPeople
        
        switch selectedFilter {
        case .all:
            return baseContacts
        case .recent:
            return baseContacts.filter { $0.lastContactDate != nil }
        case .favorites:
            // Implement favorites logic when available
            return baseContacts
        case .family:
            return baseContacts.filter { 
                $0.relationship?.lowercased().contains("family") == true ||
                $0.relationship?.lowercased().contains("parent") == true ||
                $0.relationship?.lowercased().contains("sibling") == true
            }
        case .friends:
            return baseContacts.filter { 
                $0.relationship?.lowercased().contains("friend") == true
            }
        case .work:
            return baseContacts.filter { 
                $0.relationship?.lowercased().contains("work") == true ||
                $0.relationship?.lowercased().contains("colleague") == true ||
                $0.relationship?.lowercased().contains("business") == true
            }
        }
    }
    
    private var emptyStateTitle: String {
        if !viewModel.searchText.isEmpty {
            return "No Results Found"
        } else if selectedFilter != .all {
            return "No \(selectedFilter.rawValue) Contacts"
        } else {
            return "No Contacts Yet"
        }
    }
    
    private var emptyStateMessage: String {
        if !viewModel.searchText.isEmpty {
            return "Try adjusting your search terms or filters"
        } else if selectedFilter != .all {
            return "Add contacts and organize them by relationship"
        } else {
            return "Start building your personal network by adding your first contact"
        }
    }
}

// MARK: - Preview
#Preview {
    ContactsListView(viewContext: PersistenceController.preview.container.viewContext)
}
