//
//  GlassmorphicCard.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI

struct GlassmorphicCard<Content: View>: View {
    
    // MARK: - Properties
    let content: Content
    let cornerRadius: CGFloat
    let padding: CGFloat
    let shadowStyle: ShadowStyle
    
    // MARK: - Shadow Styles
    enum ShadowStyle {
        case none
        case soft
        case medium
        case large
        
        var shadow: Shadow {
            switch self {
            case .none:
                return Shadow(color: .clear, radius: 0, x: 0, y: 0)
            case .soft:
                return DesignSystem.Shadows.soft
            case .medium:
                return DesignSystem.Shadows.medium
            case .large:
                return DesignSystem.Shadows.large
            }
        }
    }
    
    // MARK: - Initialization
    init(
        cornerRadius: CGFloat = DesignSystem.CornerRadius.large,
        padding: CGFloat = DesignSystem.Spacing.md,
        shadowStyle: ShadowStyle = .soft,
        @ViewBuilder content: () -> Content
    ) {
        self.content = content()
        self.cornerRadius = cornerRadius
        self.padding = padding
        self.shadowStyle = shadowStyle
    }
    
    // MARK: - Body
    var body: some View {
        content
            .padding(padding)
            .background(glassmorphicBackground)
            .overlay(borderOverlay)
            .shadow(
                color: shadowStyle.shadow.color,
                radius: shadowStyle.shadow.radius,
                x: shadowStyle.shadow.x,
                y: shadowStyle.shadow.y
            )
    }
    
    // MARK: - Private Views
    private var glassmorphicBackground: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(DesignSystem.Colors.glassWhite)
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(.ultraThinMaterial)
            )
    }
    
    private var borderOverlay: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .stroke(DesignSystem.Colors.glassBorder, lineWidth: 1)
    }
}

// MARK: - Specialized Card Variants

struct ContactCard<Content: View>: View {
    let content: Content
    let isSelected: Bool
    let onTap: () -> Void
    
    @State private var isPressed = false
    
    init(
        isSelected: Bool = false,
        onTap: @escaping () -> Void = {},
        @ViewBuilder content: () -> Content
    ) {
        self.content = content()
        self.isSelected = isSelected
        self.onTap = onTap
    }
    
    var body: some View {
        GlassmorphicCard(
            cornerRadius: DesignSystem.CornerRadius.large,
            padding: DesignSystem.Spacing.md,
            shadowStyle: isSelected ? .medium : .soft
        ) {
            content
        }
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .opacity(isPressed ? 0.9 : 1.0)
        .overlay(
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                .stroke(
                    isSelected ? DesignSystem.Colors.mutedGold : Color.clear,
                    lineWidth: 2
                )
        )
        .onTapGesture {
            withAnimation(DesignSystem.Animations.spring) {
                onTap()
            }
            
            // Haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        }
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(DesignSystem.Animations.easeInOut) {
                isPressed = pressing
            }
        } perform: {}
    }
}

struct SearchCard<Content: View>: View {
    let content: Content
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    var body: some View {
        GlassmorphicCard(
            cornerRadius: DesignSystem.CornerRadius.extraLarge,
            padding: DesignSystem.Spacing.sm,
            shadowStyle: .none
        ) {
            content
        }
    }
}

struct FloatingActionCard<Content: View>: View {
    let content: Content
    let onTap: () -> Void
    
    @State private var isPressed = false
    
    init(
        onTap: @escaping () -> Void,
        @ViewBuilder content: () -> Content
    ) {
        self.content = content()
        self.onTap = onTap
    }
    
    var body: some View {
        GlassmorphicCard(
            cornerRadius: DesignSystem.CornerRadius.round,
            padding: DesignSystem.Spacing.md,
            shadowStyle: .large
        ) {
            content
        }
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .onTapGesture {
            withAnimation(DesignSystem.Animations.spring) {
                onTap()
            }
            
            // Haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
            impactFeedback.impactOccurred()
        }
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(DesignSystem.Animations.easeInOut) {
                isPressed = pressing
            }
        } perform: {}
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        // Background gradient for preview
        LinearGradient(
            colors: [
                DesignSystem.Colors.primaryCream,
                DesignSystem.Colors.secondaryCream
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        VStack(spacing: DesignSystem.Spacing.lg) {
            // Regular glassmorphic card
            GlassmorphicCard {
                VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                    Text("John Doe")
                        .font(DesignSystem.Typography.title3)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                    
                    Text("Software Engineer")
                        .font(DesignSystem.Typography.callout)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            
            // Contact card
            ContactCard(isSelected: true) {
                // Tap action
            } content: {
                HStack {
                    Circle()
                        .fill(DesignSystem.Colors.mutedGold)
                        .frame(width: 50, height: 50)
                        .overlay(
                            Text("JD")
                                .font(DesignSystem.Typography.headline)
                                .foregroundColor(.white)
                        )
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Jane Doe")
                            .font(DesignSystem.Typography.title3)
                            .foregroundColor(DesignSystem.Colors.warmBlack)
                        
                        Text("Designer")
                            .font(DesignSystem.Typography.callout)
                            .foregroundColor(DesignSystem.Colors.subtleText)
                    }
                    
                    Spacer()
                }
            }
            
            // Floating action card
            FloatingActionCard {
                // Add action
            } content: {
                Image(systemName: "plus")
                    .font(.title2)
                    .foregroundColor(DesignSystem.Colors.warmBlack)
            }
        }
        .padding(DesignSystem.Spacing.lg)
    }
}
