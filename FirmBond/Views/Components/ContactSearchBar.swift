//
//  ContactSearchBar.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI

struct ContactSearchBar: View {
    
    // MARK: - Properties
    @Binding var searchText: String
    @FocusState private var isSearchFocused: Bool
    
    let placeholder: String
    let onSearchCommit: (() -> Void)?
    let onClear: (() -> Void)?
    
    @State private var isAnimating = false
    
    // MARK: - Initialization
    init(
        searchText: Binding<String>,
        placeholder: String = "Search contacts...",
        onSearchCommit: (() -> Void)? = nil,
        onClear: (() -> Void)? = nil
    ) {
        self._searchText = searchText
        self.placeholder = placeholder
        self.onSearchCommit = onSearchCommit
        self.onClear = onClear
    }
    
    // MARK: - Body
    var body: some View {
        SearchCard {
            HStack(spacing: DesignSystem.Spacing.sm) {
                // Search icon
                searchIcon
                
                // Text field
                searchTextField
                
                // Clear button
                if !searchText.isEmpty {
                    clearButton
                }
            }
        }
        .animation(DesignSystem.Animations.gentle, value: searchText.isEmpty)
        .animation(DesignSystem.Animations.gentle, value: isSearchFocused)
    }
    
    // MARK: - Private Views
    private var searchIcon: some View {
        Image(systemName: "magnifyingglass")
            .font(DesignSystem.Typography.callout)
            .foregroundColor(
                isSearchFocused ? DesignSystem.Colors.mutedGold : DesignSystem.Colors.subtleText
            )
            .scaleEffect(isAnimating ? 1.1 : 1.0)
            .animation(
                Animation.easeInOut(duration: 0.2).repeatCount(1),
                value: isAnimating
            )
    }
    
    private var searchTextField: some View {
        TextField(placeholder, text: $searchText)
            .font(DesignSystem.Typography.body)
            .foregroundColor(DesignSystem.Colors.warmBlack)
            .focused($isSearchFocused)
            .textFieldStyle(PlainTextFieldStyle())
            .onSubmit {
                onSearchCommit?()
                triggerSearchAnimation()
            }
            .onChange(of: isSearchFocused) { _, focused in
                if focused {
                    triggerSearchAnimation()
                }
            }
    }
    
    private var clearButton: some View {
        Button(action: clearSearch) {
            Image(systemName: "xmark.circle.fill")
                .font(DesignSystem.Typography.callout)
                .foregroundColor(DesignSystem.Colors.subtleText)
        }
        .buttonStyle(PlainButtonStyle())
        .transition(.scale.combined(with: .opacity))
    }
    
    // MARK: - Private Methods
    private func clearSearch() {
        withAnimation(DesignSystem.Animations.gentle) {
            searchText = ""
            isSearchFocused = false
        }
        
        onClear?()
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    private func triggerSearchAnimation() {
        withAnimation(.easeInOut(duration: 0.2)) {
            isAnimating = true
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.easeInOut(duration: 0.2)) {
                isAnimating = false
            }
        }
    }
}

// MARK: - Enhanced Search Bar with Filters
struct EnhancedContactSearchBar: View {
    
    // MARK: - Properties
    @Binding var searchText: String
    @Binding var selectedFilter: ContactFilter
    @FocusState private var isSearchFocused: Bool
    
    let onSearchCommit: (() -> Void)?
    
    // MARK: - Filter Options
    enum ContactFilter: String, CaseIterable {
        case all = "All"
        case recent = "Recent"
        case favorites = "Favorites"
        case family = "Family"
        case friends = "Friends"
        case work = "Work"
        
        var icon: String {
            switch self {
            case .all: return "person.2"
            case .recent: return "clock"
            case .favorites: return "heart"
            case .family: return "house"
            case .friends: return "person.3"
            case .work: return "briefcase"
            }
        }
    }
    
    // MARK: - Initialization
    init(
        searchText: Binding<String>,
        selectedFilter: Binding<ContactFilter>,
        onSearchCommit: (() -> Void)? = nil
    ) {
        self._searchText = searchText
        self._selectedFilter = selectedFilter
        self.onSearchCommit = onSearchCommit
    }
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // Search bar
            ContactSearchBar(
                searchText: $searchText,
                onSearchCommit: onSearchCommit
            )
            
            // Filter chips
            if !isSearchFocused {
                filterChips
                    .transition(.move(edge: .top).combined(with: .opacity))
            }
        }
        .animation(DesignSystem.Animations.gentle, value: isSearchFocused)
    }
    
    // MARK: - Private Views
    private var filterChips: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                ForEach(ContactFilter.allCases, id: \.self) { filter in
                    FilterChip(
                        filter: filter,
                        isSelected: selectedFilter == filter
                    ) {
                        withAnimation(DesignSystem.Animations.spring) {
                            selectedFilter = filter
                        }
                        
                        // Haptic feedback
                        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                        impactFeedback.impactOccurred()
                    }
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.md)
        }
    }
}

// MARK: - Filter Chip Component
private struct FilterChip: View {
    let filter: EnhancedContactSearchBar.ContactFilter
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: DesignSystem.Spacing.xs) {
                Image(systemName: filter.icon)
                    .font(.caption)
                
                Text(filter.rawValue)
                    .font(DesignSystem.Typography.caption)
            }
            .padding(.horizontal, DesignSystem.Spacing.sm)
            .padding(.vertical, DesignSystem.Spacing.xs)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .fill(
                        isSelected
                            ? DesignSystem.Colors.mutedGold.opacity(0.2)
                            : DesignSystem.Colors.glassWhite
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                            .stroke(
                                isSelected
                                    ? DesignSystem.Colors.mutedGold
                                    : DesignSystem.Colors.glassBorder,
                                lineWidth: 1
                            )
                    )
            )
            .foregroundColor(
                isSelected
                    ? DesignSystem.Colors.warmBlack
                    : DesignSystem.Colors.subtleText
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        LinearGradient(
            colors: [
                DesignSystem.Colors.primaryCream,
                DesignSystem.Colors.secondaryCream
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        VStack(spacing: DesignSystem.Spacing.xl) {
            // Basic search bar
            ContactSearchBar(
                searchText: .constant(""),
                placeholder: "Search contacts..."
            )
            
            // Enhanced search bar with filters
            EnhancedContactSearchBar(
                searchText: .constant(""),
                selectedFilter: .constant(.all)
            )
        }
        .padding(DesignSystem.Spacing.lg)
    }
}
