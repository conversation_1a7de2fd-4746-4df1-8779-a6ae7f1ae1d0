<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="1" systemVersion="11A491" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Item" representedClassName="Item" syncable="YES" codeGenerationType="class">
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
    </entity>
    <entity name="Person" representedClassName="Person" syncable="YES" codeGenerationType="class">
        <attribute name="dateAdded" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="email" optional="YES" attributeType="String"/>
        <attribute name="firstName" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="lastName" optional="YES" attributeType="String"/>
        <attribute name="lastContactDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="notes" optional="YES" attributeType="String"/>
        <attribute name="phoneNumber" optional="YES" attributeType="String"/>
        <attribute name="photoData" optional="YES" attributeType="Binary"/>
        <attribute name="relationship" optional="YES" attributeType="String"/>
        <attribute name="tags" optional="YES" attributeType="String"/>
    </entity>
    <elements>
        <element name="Item" positionX="-63" positionY="-18" width="128" height="44"/>
        <element name="Person" positionX="-54" positionY="9" width="128" height="194"/>
    </elements>
</model>