//
//  ContactsViewModel.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import CoreData
import Combine

@MainActor
class ContactsViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var people: [Person] = []
    @Published var searchText: String = ""
    @Published var selectedPerson: Person?
    @Published var isShowingAddContact: Bool = false
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let viewContext: NSManagedObjectContext
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Computed Properties
    var filteredPeople: [Person] {
        if searchText.isEmpty {
            return people.sorted { person1, person2 in
                let name1 = "\(person1.firstName ?? "") \(person1.lastName ?? "")".trimmingCharacters(in: .whitespaces)
                let name2 = "\(person2.firstName ?? "") \(person2.lastName ?? "")".trimmingCharacters(in: .whitespaces)
                return name1.localizedCaseInsensitiveCompare(name2) == .orderedAscending
            }
        } else {
            return people.filter { person in
                let fullName = "\(person.firstName ?? "") \(person.lastName ?? "")"
                let email = person.email ?? ""
                let phone = person.phoneNumber ?? ""
                let relationship = person.relationship ?? ""
                
                return fullName.localizedCaseInsensitiveContains(searchText) ||
                       email.localizedCaseInsensitiveContains(searchText) ||
                       phone.contains(searchText) ||
                       relationship.localizedCaseInsensitiveContains(searchText)
            }.sorted { person1, person2 in
                let name1 = "\(person1.firstName ?? "") \(person1.lastName ?? "")".trimmingCharacters(in: .whitespaces)
                let name2 = "\(person2.firstName ?? "") \(person2.lastName ?? "")".trimmingCharacters(in: .whitespaces)
                return name1.localizedCaseInsensitiveCompare(name2) == .orderedAscending
            }
        }
    }
    
    var recentContacts: [Person] {
        return people
            .filter { $0.lastContactDate != nil }
            .sorted { ($0.lastContactDate ?? Date.distantPast) > ($1.lastContactDate ?? Date.distantPast) }
            .prefix(5)
            .map { $0 }
    }
    
    // MARK: - Initialization
    init(viewContext: NSManagedObjectContext) {
        self.viewContext = viewContext
        setupSearchDebouncing()
        fetchPeople()
    }
    
    // MARK: - Private Methods
    private func setupSearchDebouncing() {
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    func fetchPeople() {
        isLoading = true
        errorMessage = nil
        
        let request: NSFetchRequest<Person> = Person.fetchRequest()
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \Person.firstName, ascending: true),
            NSSortDescriptor(keyPath: \Person.lastName, ascending: true)
        ]
        
        do {
            people = try viewContext.fetch(request)
            isLoading = false
        } catch {
            errorMessage = "Failed to fetch contacts: \(error.localizedDescription)"
            isLoading = false
        }
    }
    
    func addPerson(
        firstName: String,
        lastName: String,
        email: String? = nil,
        phoneNumber: String? = nil,
        relationship: String? = nil,
        notes: String? = nil,
        photoData: Data? = nil
    ) {
        let newPerson = Person(context: viewContext)
        newPerson.id = UUID()
        newPerson.firstName = firstName.trimmingCharacters(in: .whitespaces)
        newPerson.lastName = lastName.trimmingCharacters(in: .whitespaces)
        newPerson.email = email?.trimmingCharacters(in: .whitespaces)
        newPerson.phoneNumber = phoneNumber?.trimmingCharacters(in: .whitespaces)
        newPerson.relationship = relationship?.trimmingCharacters(in: .whitespaces)
        newPerson.notes = notes?.trimmingCharacters(in: .whitespaces)
        newPerson.photoData = photoData
        newPerson.dateAdded = Date()
        
        saveContext()
    }
    
    func updatePerson(
        _ person: Person,
        firstName: String,
        lastName: String,
        email: String? = nil,
        phoneNumber: String? = nil,
        relationship: String? = nil,
        notes: String? = nil,
        photoData: Data? = nil
    ) {
        person.firstName = firstName.trimmingCharacters(in: .whitespaces)
        person.lastName = lastName.trimmingCharacters(in: .whitespaces)
        person.email = email?.trimmingCharacters(in: .whitespaces)
        person.phoneNumber = phoneNumber?.trimmingCharacters(in: .whitespaces)
        person.relationship = relationship?.trimmingCharacters(in: .whitespaces)
        person.notes = notes?.trimmingCharacters(in: .whitespaces)
        
        if let photoData = photoData {
            person.photoData = photoData
        }
        
        saveContext()
    }
    
    func deletePerson(_ person: Person) {
        viewContext.delete(person)
        saveContext()
    }
    
    func updateLastContactDate(for person: Person) {
        person.lastContactDate = Date()
        saveContext()
    }
    
    func clearSearch() {
        searchText = ""
    }
    
    func selectPerson(_ person: Person) {
        selectedPerson = person
        updateLastContactDate(for: person)
    }
    
    // MARK: - Private Helpers
    private func saveContext() {
        do {
            try viewContext.save()
            fetchPeople() // Refresh the list
        } catch {
            errorMessage = "Failed to save: \(error.localizedDescription)"
        }
    }
}

// MARK: - Person Extensions
extension Person {
    var fullName: String {
        let first = firstName ?? ""
        let last = lastName ?? ""
        return "\(first) \(last)".trimmingCharacters(in: .whitespaces)
    }
    
    var initials: String {
        let first = firstName?.first?.uppercased() ?? ""
        let last = lastName?.first?.uppercased() ?? ""
        return "\(first)\(last)"
    }
    
    var displayPhoneNumber: String {
        guard let phone = phoneNumber, !phone.isEmpty else { return "" }
        
        // Format phone number for display
        let digits = phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        
        if digits.count == 10 {
            let areaCode = String(digits.prefix(3))
            let firstThree = String(digits.dropFirst(3).prefix(3))
            let lastFour = String(digits.suffix(4))
            return "(\(areaCode)) \(firstThree)-\(lastFour)"
        }
        
        return phone
    }
    
    var hasContactInfo: Bool {
        return !(phoneNumber?.isEmpty ?? true) || !(email?.isEmpty ?? true)
    }
}
